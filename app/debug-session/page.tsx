'use client';

import { useSession } from 'next-auth/react';
import { useState } from 'react';

export default function DebugSessionPage() {
  const { data: session, status } = useSession();
  const [showRawData, setShowRawData] = useState(false);

  if (status === 'loading') {
    return <div className="p-8">Loading session...</div>;
  }

  if (status === 'unauthenticated') {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Debug Session - Not Authenticated</h1>
        <p>Please sign in to view session data.</p>
        <a href="/auth/sign-in" className="text-blue-600 hover:underline">
          Go to Sign In
        </a>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Session Data</h1>
      
      <div className="space-y-6">
        {/* User Basic Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">User Basic Info</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>ID:</strong> {session?.user?.id || 'N/A'}
            </div>
            <div>
              <strong>Name:</strong> {session?.user?.name || 'N/A'}
            </div>
            <div>
              <strong>Email:</strong> {session?.user?.email || 'N/A'}
            </div>
            <div>
              <strong>Role:</strong> {session?.user?.role || 'N/A'}
            </div>
            <div>
              <strong>School ID:</strong> {session?.user?.schoolId || 'N/A'}
            </div>
            <div>
              <strong>Is Login:</strong> {session?.user?.isLogin ? 'Yes' : 'No'}
            </div>
          </div>
        </div>

        {/* School Info */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">School Information</h2>
          {session?.user?.school ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>School ID:</strong> {session.user.school.id || 'N/A'}
              </div>
              <div>
                <strong>School Name:</strong> {session.user.school.name || 'N/A'}
              </div>
              <div>
                <strong>Address:</strong> {session.user.school.address || 'N/A'}
              </div>
              <div>
                <strong>Phone:</strong> {session.user.school.phoneNumber || 'N/A'}
              </div>
              <div>
                <strong>Email:</strong> {session.user.school.email || 'N/A'}
              </div>
              <div>
                <strong>Registered Number:</strong> {session.user.school.registeredNumber || 'N/A'}
              </div>
            </div>
          ) : (
            <p className="text-gray-600">No school data available</p>
          )}
        </div>

        {/* Brand Info */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Brand Information</h2>
          {session?.user?.school?.brand ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Brand ID:</strong> {session.user.school.brand.id || 'N/A'}
              </div>
              <div>
                <strong>Logo:</strong> {session.user.school.brand.logo || 'N/A'}
              </div>
              <div>
                <strong>Color:</strong> {session.user.school.brand.color || 'N/A'}
              </div>
              <div>
                <strong>Image:</strong> {session.user.school.brand.image || 'N/A'}
              </div>
            </div>
          ) : (
            <p className="text-gray-600">No brand data available</p>
          )}
        </div>

        {/* Raw Session Data */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold">Raw Session Data</h2>
            <button
              onClick={() => setShowRawData(!showRawData)}
              className="px-3 py-1 bg-yellow-200 hover:bg-yellow-300 rounded text-sm"
            >
              {showRawData ? 'Hide' : 'Show'} Raw Data
            </button>
          </div>
          {showRawData && (
            <pre className="bg-white p-3 rounded border overflow-auto text-xs">
              {JSON.stringify(session, null, 2)}
            </pre>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-4">
          <a
            href="/auth/sign-out"
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Sign Out
          </a>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
}
