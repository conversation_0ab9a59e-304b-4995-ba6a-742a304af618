@import 'tailwindcss';

@plugin "daisyui" {
  themes: light --default;
};

@theme {
  /* Base Color Palette - Primary Colors */
  --color-black: #000000;
  --color-white: #ffffff;

  /* Blue Accent Palette */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-800: #1e40af;

  /* Gray Neutrals */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-600: #4b5563;
  --color-gray-800: #1f2937;

  /* Semantic Color Tokens */
  --color-primary-action: var(--color-black);
  --color-background-default: var(--color-white);
  --color-link-default: var(--color-blue-600);
  --color-link-hover: var(--color-blue-500);
  --color-button-pill-bg: var(--color-blue-800);
  --color-button-pill-text: var(--color-white);
  --color-tag-bg: var(--color-blue-800);
  --color-tag-text: var(--color-white);
  --color-accent-bg-light: var(--color-blue-100);
  --color-section-bg-accent: var(--color-blue-50);
  --color-interactive-hover-bg-light: var(--color-blue-200);
  --color-text-primary: var(--color-gray-800);
  --color-text-secondary: var(--color-gray-600);
  --color-section-bg-neutral-alt: var(--color-gray-100);
  --color-background-subtle: var(--color-gray-50);

  /* Typography Scale */
  --text-small: 0.75rem;
  --text-sm: 0.875rem;
  --text-large: 1.125rem;
  --text-xlarge: 1.75rem;
}

@plugin "daisyui/theme" {
  name: 'light';
  default: true;

  /* Map DaisyUI theme colors to our new system */
  --color-primary: var(--color-primary-action);
  --color-primary-content: var(--color-background-default);
  --color-secondary: var(--color-text-secondary);
  --color-secondary-content: var(--color-background-default);
  --color-accent: var(--color-link-default);
  --color-accent-content: var(--color-background-default);
  --color-neutral: var(--color-text-primary);
  --color-neutral-content: var(--color-background-default);
  --color-base-100: var(--color-background-default);
  --color-base-200: var(--color-background-subtle);
  --color-base-300: var(--color-section-bg-neutral-alt);
  --color-base-content: var(--color-text-primary);
  --color-info: var(--color-blue-500);
  --color-info-content: var(--color-background-default);
  --color-success: #10b981;
  --color-success-content: var(--color-background-default);
  --color-warning: #f59e0b;
  --color-warning-content: var(--color-background-default);
  --color-error: #ef4444;
  --color-error-content: var(--color-background-default);
}

/* Custom sticky header with -16px offset */
.sticky-header-offset {
  position: sticky;
  top: -16px;
  z-index: 10;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--color-section-bg-neutral-alt);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-primary);
}
